
import { Outlet } from "react-router-dom"
import { Sidebar } from "@/components/Sidebar"
import { UserMenu } from "@/components/auth/UserMenu"

export function Layout() {
  return (
    <div className="flex h-screen bg-gray-50/30 dark:bg-gray-900">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200/60 dark:border-gray-800/60 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl supports-[backdrop-filter]:bg-white/80 dark:supports-[backdrop-filter]:bg-gray-900/80">
          <div className="flex h-16 items-center justify-between px-6">
            <div className="flex items-center space-x-3">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">SPQR Business Hub</h1>
              <div className="hidden sm:flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">AI Active</span>
              </div>
            </div>
            <UserMenu />
          </div>
        </header>
        <main className="flex-1 overflow-y-auto bg-gray-50/30 dark:bg-gray-900">
          <Outlet />
        </main>
      </div>
    </div>
  )
}
