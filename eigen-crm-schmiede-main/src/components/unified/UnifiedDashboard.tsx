
import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  Brain,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  Zap,
  Shield,
  Clock
} from 'lucide-react'
import { unifiedDatabase } from '@/services/unified/UnifiedDatabaseService'
import { unifiedLLM } from '@/services/unified/UnifiedLLMService'
import { unifiedAI } from '@/services/unified/UnifiedAIOrchestrator'
import type { UnifiedProject, UnifiedTask, UnifiedAgent, UnifiedMetrics } from '@/types/unified'

export const UnifiedDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [projects, setProjects] = useState<UnifiedProject[]>([])
  const [tasks, setTasks] = useState<UnifiedTask[]>([])
  const [agents, setAgents] = useState<UnifiedAgent[]>([])
  const [metrics, setMetrics] = useState<UnifiedMetrics | null>(null)
  const [systemStatus, setSystemStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      const [projectsRes, tasksRes, agentsRes, systemRes, aiMetricsRes] = await Promise.all([
        unifiedDatabase.getProjects(),
        unifiedDatabase.getTasks(),
        unifiedDatabase.getAgents(),
        unifiedLLM.getSystemStatus(),
        unifiedAI.getAIMetrics()
      ])

      if (projectsRes.success) setProjects(projectsRes.data || [])
      if (tasksRes.success) setTasks(tasksRes.data || [])
      if (agentsRes.success) setAgents(agentsRes.data || [])
      if (systemRes.success) setSystemStatus(systemRes.data)
      if (aiMetricsRes.success) setMetrics(aiMetricsRes.data?.llm)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
      case 'healthy':
        return 'default'
      case 'on-hold':
      case 'degraded':
        return 'secondary'
      case 'cancelled':
      case 'down':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">Loading dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
              Business Intelligence Hub
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg">
              Unified dashboard for all your business operations and AI systems
            </p>
          </div>
          <Button onClick={loadDashboardData} variant="outline" className="hover-lift">
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Projects</CardTitle>
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 dark:text-white">{projects.filter(p => p.status === 'active').length}</div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {projects.length} total projects
              </p>
            </CardContent>
          </Card>

          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">AI Agents</CardTitle>
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                <Brain className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 dark:text-white">{agents.filter(a => a.status === 'running').length}</div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {agents.length} total agents
              </p>
            </CardContent>
          </Card>

          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">LLM Requests</CardTitle>
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
                <Zap className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900 dark:text-white">{metrics?.totalRequests || 0}</div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {((metrics?.successRate || 0) * 100).toFixed(1)}% success rate
              </p>
            </CardContent>
          </Card>

          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between pb-3">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">System Health</CardTitle>
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <Shield className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                {(metrics?.systemHealth || 100).toFixed(0)}%
              </div>
              <Progress value={metrics?.systemHealth || 100} className="mt-3" />
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <TabsList className="grid w-full grid-cols-5 bg-gray-100/80 dark:bg-gray-800/50 p-1 rounded-2xl">
            <TabsTrigger value="overview" className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg dark:data-[state=active]:bg-gray-700">Overview</TabsTrigger>
            <TabsTrigger value="projects" className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg dark:data-[state=active]:bg-gray-700">Projects</TabsTrigger>
            <TabsTrigger value="agents" className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg dark:data-[state=active]:bg-gray-700">AI Agents</TabsTrigger>
            <TabsTrigger value="llm" className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg dark:data-[state=active]:bg-gray-700">LLM Status</TabsTrigger>
            <TabsTrigger value="analytics" className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-lg dark:data-[state=active]:bg-gray-700">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card className="hover-lift">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">Recent Projects</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">Latest project activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {projects.slice(0, 5).map((project) => (
                      <div key={project.id} className="flex items-center justify-between p-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/30 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-colors">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{project.name}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {project.progress}% complete
                          </p>
                        </div>
                        <Badge variant={getStatusColor(project.status)} className="rounded-lg">
                          {project.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="hover-lift">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">AI Agent Status</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">Current agent activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {agents.slice(0, 5).map((agent) => (
                      <div key={agent.id} className="flex items-center justify-between p-3 rounded-xl bg-gray-50/50 dark:bg-gray-800/30 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-colors">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {agent.type} • {agent.performance.tasksCompleted} tasks
                          </p>
                        </div>
                        <Badge variant={getStatusColor(agent.status)} className="rounded-lg">
                          {agent.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="projects" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Management</CardTitle>
                <CardDescription>Overview of all projects and tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {projects.map((project) => (
                    <div key={project.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{project.name}</h4>
                        <Badge variant={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {project.description}
                      </p>
                      <div className="flex items-center justify-between text-sm">
                        <span>Progress: {project.progress}%</span>
                        <span>Budget: ${project.budget.toLocaleString()}</span>
                      </div>
                      <Progress value={project.progress} className="mt-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="agents" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Agent Management</CardTitle>
                <CardDescription>Monitor and manage AI agents</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agents.map((agent) => (
                    <div key={agent.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{agent.name}</h4>
                        <Badge variant={getStatusColor(agent.status)}>
                          {agent.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Type: {agent.type}
                      </p>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Tasks Completed:</span>
                          <span>{agent.performance.tasksCompleted}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Success Rate:</span>
                          <span>{(agent.performance.successRate * 100).toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Response:</span>
                          <span>{agent.performance.averageResponseTime}ms</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="llm" className="space-y-6">
            {systemStatus && (
              <Card>
                <CardHeader>
                  <CardTitle>LLM System Status</CardTitle>
                  <CardDescription>Provider health and performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-4">Local LLM (Ollama)</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant={systemStatus.providers.ollama.available ? 'default' : 'destructive'}>
                            {systemStatus.providers.ollama.available ? 'Available' : 'Offline'}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Models:</span>
                          <span>{systemStatus.providers.ollama.models.length}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-4">Cloud LLM</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="default">Available</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Models:</span>
                          <span>{systemStatus.providers.cloud.models.length}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {metrics && (
                    <div className="mt-6 pt-6 border-t">
                      <h4 className="font-medium mb-4">Performance Metrics</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold">{metrics.totalRequests}</div>
                          <p className="text-sm text-muted-foreground">Total Requests</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">
                            {(metrics.successRate * 100).toFixed(1)}%
                          </div>
                          <p className="text-sm text-muted-foreground">Success Rate</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{metrics.averageResponseTime}ms</div>
                          <p className="text-sm text-muted-foreground">Avg Response</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">${metrics.costToday.toFixed(2)}</div>
                          <p className="text-sm text-muted-foreground">Cost Today</p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Analytics</CardTitle>
                <CardDescription>Comprehensive business insights and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center p-4 border rounded-lg">
                    <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <div className="text-2xl font-bold">{projects.length}</div>
                    <p className="text-sm text-muted-foreground">Active Projects</p>
                  </div>

                  <div className="text-center p-4 border rounded-lg">
                    <Clock className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <div className="text-2xl font-bold">
                      {tasks.filter(t => t.status === 'completed').length}
                    </div>
                    <p className="text-sm text-muted-foreground">Completed Tasks</p>
                  </div>

                  <div className="text-center p-4 border rounded-lg">
                    <DollarSign className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                    <div className="text-2xl font-bold">
                      ${projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Budget</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
