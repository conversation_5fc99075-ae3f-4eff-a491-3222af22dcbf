
import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  CheckSquare,
  Calendar,
  Settings,
  Menu,
  X,
  Briefcase,
  FileText,
  BarChart3,
  Shield,
  Truck,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

const navigation = [
  { name: 'Dashboard', icon: LayoutDashboard, href: '/dashboard', path: 'dashboard' },
  { name: 'Projects', icon: Briefcase, href: '/projects', path: 'projects' },
  { name: 'People', icon: Users, href: '/people', path: 'people' },
  { name: 'Tasks', icon: CheckSquare, href: '/tasks', path: 'tasks' },
  { name: 'Calendar', icon: Calendar, href: '/calendar', path: 'calendar' },
  { name: 'Documents', icon: FileText, href: '/documents', path: 'documents' },
  { name: 'Fleet', icon: Truck, href: '/fleet', path: 'fleet' },
  { name: 'Reports', icon: BarChart3, href: '/reports', path: 'reports' },
  { name: 'Security', icon: Shield, href: '/security', path: 'security' },
  { name: 'Settings', icon: Settings, href: '/settings', path: 'settings' },
];

export const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const currentPath = location.pathname.split('/')[1] || 'dashboard';

  const handleNavigation = (href: string) => {
    navigate(href);
  };

  return (
    <div className={cn(
      "flex flex-col bg-white/95 backdrop-blur-xl border-r border-gray-200/60 transition-all duration-300 shadow-xl",
      "dark:bg-gray-900/95 dark:border-gray-800/60",
      isCollapsed ? "w-16" : "w-80"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200/60 dark:border-gray-800/60">
        {!isCollapsed && (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div className="flex flex-col">
              <span className="font-semibold text-gray-900 dark:text-white text-lg">SPQR</span>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">Premium CRM</span>
            </div>
          </div>
        )}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 rounded-xl text-gray-500 hover:text-gray-900 hover:bg-gray-100/80 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800/50 transition-all duration-200 hover:scale-105 active:scale-95"
        >
          {isCollapsed ? <Menu size={20} /> : <X size={20} />}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {navigation.map((item) => (
          <button
            key={item.name}
            onClick={() => handleNavigation(item.href)}
            className={cn(
              "w-full flex items-center rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 group",
              isCollapsed ? "justify-center" : "",
              currentPath === item.path
                ? "bg-blue-500 text-white shadow-lg hover:bg-blue-600 hover:shadow-xl"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-100/80 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800/50"
            )}
          >
            <item.icon className="h-5 w-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" />
            {!isCollapsed && <span className="ml-3 truncate text-left">{item.name}</span>}
          </button>
        ))}
      </nav>

      {/* User Profile */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-200/60 dark:border-gray-800/60 bg-gray-50/50 dark:bg-gray-800/30">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white text-sm font-medium">JD</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                John Doe
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                <EMAIL>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
