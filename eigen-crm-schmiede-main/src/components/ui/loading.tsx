
import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

export const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  className,
  text
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex flex-col items-center gap-4">
        <div
          className={cn(
            'animate-spin rounded-full border-2 border-gray-200 dark:border-gray-700 border-t-blue-500',
            sizeClasses[size]
          )}
        />
        {text && (
          <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">{text}</p>
        )}
      </div>
    </div>
  );
};

export const PageLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex items-center justify-center min-h-[400px]">
    <Loading size="lg" text={text} />
  </div>
);

export const CardLoading: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded-xl w-3/4 mb-3"></div>
    <div className="h-3 bg-gray-100 dark:bg-gray-800 rounded-lg w-1/2"></div>
  </div>
);
