
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Apple-inspired Premium Color System */
    --background: 251 251 253;
    --foreground: 28 25 23;

    --card: 255 255 255;
    --card-foreground: 28 25 23;

    --popover: 255 255 255;
    --popover-foreground: 28 25 23;

    /* Premium Blue - Apple-like system blue */
    --primary: 213 94% 68%;
    --primary-foreground: 255 255 255;

    /* Sophisticated Gray */
    --secondary: 244 244 246;
    --secondary-foreground: 82 82 91;

    /* Subtle Background */
    --muted: 248 250 252;
    --muted-foreground: 100 116 139;

    /* Accent - Refined Purple */
    --accent: 239 68% 68%;
    --accent-foreground: 255 255 255;

    /* Refined Destructive */
    --destructive: 0 84% 60%;
    --destructive-foreground: 255 255 255;

    /* Minimal Borders */
    --border: 229 231 235;
    --input: 229 231 235;
    --ring: 213 94% 68%;

    /* Apple-like Radius */
    --radius: 0.75rem;

    /* Premium Sidebar */
    --sidebar-background: 255 255 255;
    --sidebar-foreground: 28 25 23;
    --sidebar-primary: 213 94% 68%;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 248 250 252;
    --sidebar-accent-foreground: 82 82 91;
    --sidebar-border: 229 231 235;
    --sidebar-ring: 213 94% 68%;

    /* Premium Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Apple-like Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
  }

  .dark {
    /* Dark Mode - Apple-inspired */
    --background: 17 17 17;
    --foreground: 250 250 250;

    --card: 23 23 23;
    --card-foreground: 250 250 250;

    --popover: 23 23 23;
    --popover-foreground: 250 250 250;

    --primary: 213 94% 68%;
    --primary-foreground: 17 17 17;

    --secondary: 39 39 42;
    --secondary-foreground: 161 161 170;

    --muted: 28 28 28;
    --muted-foreground: 113 113 122;

    --accent: 239 68% 68%;
    --accent-foreground: 17 17 17;

    --destructive: 0 84% 60%;
    --destructive-foreground: 250 250 250;

    --border: 39 39 42;
    --input: 39 39 42;
    --ring: 213 94% 68%;

    --sidebar-background: 23 23 23;
    --sidebar-foreground: 250 250 250;
    --sidebar-primary: 213 94% 68%;
    --sidebar-primary-foreground: 17 17 17;
    --sidebar-accent: 28 28 28;
    --sidebar-accent-foreground: 161 161 170;
    --sidebar-border: 39 39 42;
    --sidebar-ring: 213 94% 68%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Inter', system-ui, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Apple-inspired Typography Scale */
  .text-display {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }

  .text-headline {
    font-size: 2.25rem;
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.015em;
  }

  .text-title {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.01em;
  }

  .text-body {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.005em;
  }

  .text-caption {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    letter-spacing: -0.003em;
  }

  .text-footnote {
    font-size: 0.75rem;
    font-weight: 400;
    line-height: 1.3;
    letter-spacing: 0;
  }
}

@layer components {
  /* Premium Glass Morphism Cards */
  .glass-card {
    @apply bg-white/90 backdrop-blur-xl border border-white/20 shadow-xl rounded-2xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .glass-card-dark {
    @apply bg-black/20 backdrop-blur-xl border border-white/10 shadow-2xl rounded-2xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  /* Premium Card Variants */
  .premium-card {
    @apply bg-white border border-gray-200/60 shadow-lg rounded-2xl transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
  }

  .premium-card-elevated {
    @apply bg-white border border-gray-200/60 shadow-xl rounded-2xl transition-all duration-300 hover:shadow-2xl hover:-translate-y-1;
  }

  /* Apple-style Buttons */
  .btn-primary-apple {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl active:scale-95;
  }

  .btn-secondary-apple {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium px-6 py-3 rounded-xl transition-all duration-200 border border-gray-200 hover:border-gray-300 active:scale-95;
  }

  .btn-ghost-apple {
    @apply bg-transparent hover:bg-gray-100 text-gray-700 font-medium px-6 py-3 rounded-xl transition-all duration-200 active:scale-95;
  }

  /* Premium Sidebar Styles */
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group;
  }

  .sidebar-item-active {
    @apply bg-blue-500 text-white shadow-lg;
  }

  .sidebar-item-inactive {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100/80;
  }

  /* Premium Form Elements */
  .input-apple {
    @apply w-full px-4 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400;
  }

  .select-apple {
    @apply w-full px-4 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 appearance-none;
  }

  /* Micro-interactions */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-xl;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105 active:scale-95;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25;
  }

  /* Premium Animations */
  .fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }

  .scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  .slide-in-right {
    animation: slideInRight 0.4s ease-out forwards;
  }

  .bounce-in {
    animation: bounceIn 0.6s ease-out forwards;
  }

  /* Loading States */
  .skeleton {
    @apply bg-gray-200 rounded-xl animate-pulse;
  }

  .skeleton-dark {
    @apply bg-gray-700 rounded-xl animate-pulse;
  }

  /* Premium Spacing */
  .section-padding {
    @apply px-6 py-8 lg:px-8 lg:py-12;
  }

  .card-padding {
    @apply p-6 lg:p-8;
  }

  .content-padding {
    @apply px-4 py-6 lg:px-6 lg:py-8;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }
}

/* Keyframes for Premium Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
